import './style.css';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter as Router, Routes, Route, Link, NavLink } from 'react-router-dom';

// Import components
import Home from './src/components/Home';
import ClientAcquisition from './src/components/ClientAcquisition';
import CustomerRetention from './src/components/CustomerRetention';
import Tools from './src/components/Tools';
import QuestionnairePage from './src/components/QuestionnairePage';
import CompanyProfile from './src/components/CompanyProfile';

function App() {
  return (
    <Router>
      <div className="min-h-screen flex flex-col items-center bg-white text-gray-900">
        <div className="w-full max-w-4xl p-8 rounded bg-white mt-8">
          <h1 className="raleway-title-h0 mb-2">Omega Praxis - Market Intelligence</h1>
          
          
          {/* Navigation Menu */}
          <nav className="flex border-b mb-6">
            <NavLink 
              to="/" 
              className={({ isActive }) => 
                `py-2 px-4 mr-4 raleway-menu ${isActive 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-blue-400'}`
              }
              end
            >
              Home
            </NavLink>
            <NavLink 
              to="/client-acquisition" 
              className={({ isActive }) => 
                `py-2 px-4 mr-4 raleway-menu ${isActive 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-blue-400'}`
              }
            >
              Client Acquisition
            </NavLink>
            <NavLink 
              to="/customer-retention" 
              className={({ isActive }) => 
                `py-2 px-4 mr-4 raleway-menu ${isActive 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-blue-400'}`
              }
            >
              Customer Retention
            </NavLink>
            <NavLink 
              to="/tools" 
              className={({ isActive }) => 
                `py-2 px-4 mr-4 raleway-menu ${isActive 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-blue-400'}`
              }
            >
              Tools
            </NavLink>
            <NavLink 
              to="/questionnaires" 
              className={({ isActive }) => 
                `py-2 px-4 mr-4 raleway-menu ${isActive 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-blue-400'}`
              }
            >
              Questionnaires
            </NavLink>
            <NavLink 
              to="/company-profile" 
              className={({ isActive }) => 
                `py-2 px-4 mr-4 raleway-menu ${isActive 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-blue-400'}`
              }
            >
              Company Profile
            </NavLink>
          </nav>
        </div>
        
        {/* Routes */}
        <div className="w-full max-w-4xl mb-8">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/client-acquisition" element={<ClientAcquisition />} />
            <Route path="/customer-retention" element={<CustomerRetention />} />
            <Route path="/tools" element={<Tools />} />
            <Route path="/questionnaires" element={<QuestionnairePage />} />
            <Route path="/company-profile" element={<CompanyProfile />} />
          </Routes>
        </div>
      </div>
    </Router>
  );
}

const root = createRoot(document.getElementById('root'));
root.render(<App />);