import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '../supabase/client';

// Create the auth context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Auth provider component
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [fallbackMode, setFallbackMode] = useState(false);

  // Check for user session on initial load
  useEffect(() => {
    async function getInitialSession() {
      try {
        setLoading(true);
        
        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          throw sessionError;
        }
        
        if (session) {
          const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();
          
          if (userError) {
            throw userError;
          }
          
          setUser(currentUser);
        }
      } catch (err) {
        console.error('Error getting initial session:', err);
        console.log('Enabling fallback mode - app will work without authentication');
        setError(err.message);
        setFallbackMode(true);
        // In fallback mode, create a mock user so the app works
        setUser({ id: 'fallback-user', email: '<EMAIL>', fallback: true });
      } finally {
        setLoading(false);
      }
    }
    
    getInitialSession();
    
    // Set up auth state change listener (only if not in fallback mode)
    let subscription = null;
    if (!fallbackMode) {
      try {
        const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('Auth state changed:', event, session);

            if (session) {
              setUser(session.user);
            } else {
              setUser(null);
            }

            setLoading(false);
          }
        );
        subscription = authSubscription;
      } catch (err) {
        console.error('Error setting up auth listener:', err);
      }
    }
    
    // Clean up subscription on unmount
    return () => {
      if (subscription) subscription.unsubscribe();
    };
  }, []);
  
  // Sign in with email and password
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error signing in:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Sign up with email and password
  const signUp = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error signing up:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw error;
      }
    } catch (err) {
      console.error('Error signing out:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Reset password
  const resetPassword = async (email) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error resetting password:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Update user profile
  const updateProfile = async (updates) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.updateUser(updates);
      
      if (error) {
        throw error;
      }
      
      setUser(data.user);
      return data;
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Context value
  const value = {
    user,
    loading,
    error,
    fallbackMode,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    setError
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
