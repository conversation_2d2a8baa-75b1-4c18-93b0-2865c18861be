import React, { useState, useRef } from 'react';
import DOMPurify from 'dompurify';
import config from '../config';
import OpenAI from 'openai';

// Simple markdown renderer function
function renderMarkdown(text) {
  if (!text) return '';
  
  // Convert headers
  text = text.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  text = text.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  text = text.replace(/^# (.*$)/gm, '<h1>$1</h1>');
  
  // Convert bold and italic
  text = text.replace(/\*\*(.*)\*\*/gm, '<strong>$1</strong>');
  text = text.replace(/\*(.*)\*/gm, '<em>$1</em>');
  
  // Convert lists
  text = text.replace(/^\* (.*$)/gm, '<ul><li>$1</li></ul>');
  text = text.replace(/^\d\. (.*$)/gm, '<ol><li>$1</li></ol>');
  
  // Convert code blocks
  text = text.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
  text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
  
  // Convert line breaks
  text = text.replace(/\n/g, '<br>');
  
  return text;
}

export default function AIAgent({ title, description, contextPrompt }) {
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [loading, setLoading] = useState(false);
  const [isAIResponse, setIsAIResponse] = useState(false);
  const [selectedModel, setSelectedModel] = useState('gemini-1.5-pro');
  const [thinkingMode, setThinkingMode] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [documentName, setDocumentName] = useState('');
  const [currentModel, setCurrentModel] = useState('');
  const [openaiApiKey, setOpenaiApiKey] = useState('');
  const [showApiKeyDialog, setShowApiKeyDialog] = useState(false);
  const [lastQuestion, setLastQuestion] = useState('');
  const [apiKeyInputValue, setApiKeyInputValue] = useState('');
  const [apiKeyError, setApiKeyError] = useState(null);

  // Group models by provider
  const modelOptions = {
    '🧠 Thinking Models': [
      { value: 'o1-preview', label: 'OpenAI o1-preview (Best Reasoning)' },
      { value: 'o1-mini', label: 'OpenAI o1-mini (Fast Reasoning)' },
      { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet (Excellent Reasoning)' },
      { value: 'deepseek-r1', label: 'DeepSeek R1 (Budget Reasoning)' }
    ],
    'OpenAI': [
      { value: 'gpt-4o', label: 'GPT-4o' },
      { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
      { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' }
    ],
    'Google Gemini': [
      { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro' },
      { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash' },
      { value: 'gemini-pro', label: 'Gemini Pro' }
    ],
    'Anthropic': [
      { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet' },
      { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus' },
      { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku' }
    ],
    'DeepSeek': [
      { value: 'deepseek-chat', label: 'DeepSeek Chat' },
      { value: 'deepseek-r1', label: 'DeepSeek R1 (Reasoning)' }
    ],
    'Groq': [
      { value: 'llama3-70b-8192', label: 'Llama 3 70B' },
      { value: 'llama3-8b-8192', label: 'Llama 3 8B' },
      { value: 'mixtral-8x7b-32768', label: 'Mixtral 8x7B' }
    ]
  };
  
  // Function to handle saving the document
  const handleSaveDocument = () => {
    if (!documentName.trim()) {
      alert('Please enter a document name');
      return;
    }
    
    try {
      console.log('Saving document with name:', documentName);
      
      // Format the content with the question in Markdown format
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const safeDocName = documentName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      const filename = `${safeDocName}_${timestamp}.md`;
      
      // Create markdown content
      const markdownContent = `# ${documentName}

## Question
${lastQuestion || 'N/A'}

## Answer
${answer}`;
      
      // Create a blob with the markdown content
      const blob = new Blob([markdownContent], { type: 'text/markdown' });
      
      // Create a download link and trigger the download
      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = filename;
      
      // Append to the body, click, and remove
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      
      // Clean up the object URL
      URL.revokeObjectURL(downloadLink.href);
      
      console.log('Document downloaded successfully:', filename);
      setShowSaveDialog(false);
      setDocumentName('');
      // No alert confirmation
    } catch (error) {
      console.error('Error saving document:', error);
      alert(`Error saving document: ${error.message}`);
    }
  };
  
  // Function to format the answer text with enhanced titles
  const formatAnswerWithBoldTitles = (text) => {
    if (!text) return '';
    
    // First convert markdown to HTML
    let formattedText = renderMarkdown(text);
    
    // Format titles in THINKING section if thinking mode is enabled
    if (thinkingMode) {
      formattedText = formattedText.replace(/THINKING:/g, '<span class="text-xl font-bold block mt-4 mb-2 text-blue-700">THINKING:</span>');
      formattedText = formattedText.replace(/ANSWER:/g, '<span class="text-xl font-bold block mt-4 mb-2 text-green-700">ANSWER:</span>');
    }
    
    // Format common title patterns (numbered or with colons)
    formattedText = formattedText.replace(/^(\d+\.)\s+([^\n]+)/gm, '<span class="text-lg font-bold block mt-3 mb-1">$1 $2</span>');
    formattedText = formattedText.replace(/^([A-Za-z\s]+):\s*$/gm, '<span class="text-lg font-bold block mt-3 mb-1">$1:</span>');
    
    // Format titles that are on their own line and followed by content
    formattedText = formattedText.replace(/^([A-Z][A-Za-z\s]+):\s*$/gm, '<span class="text-lg font-bold block mt-3 mb-1">$1:</span>');
    
    // Sanitize the HTML to prevent XSS attacks
    return DOMPurify.sanitize(formattedText);
  };

  // Client-side OpenAI API call function
  const callOpenAIDirectly = async (userQuestion, model, useThinkingMode) => {
    console.log('Calling OpenAI directly from client with model:', model);
    
    if (!openaiApiKey || !openaiApiKey.startsWith('sk-')) {
      throw new Error('Valid OpenAI API key is required');
    }
    
    // Create OpenAI client with user-provided API key
    const openai = new OpenAI({
      apiKey: openaiApiKey,
      dangerouslyAllowBrowser: true // Required for client-side usage
    });
    
    // Construct the prompt based on thinking mode
    let prompt = userQuestion;
    
    // Add context from the page if available
    if (contextPrompt) {
      prompt = `${contextPrompt}\n\nQuestion: ${userQuestion}`;
    }
    
    if (useThinkingMode) {
      prompt = `I want you to answer the following question about ${title || 'market research'}. First, share your thinking process labeled as "THINKING:" where you explore different aspects and considerations. Then, provide your final answer labeled as "ANSWER:". Here's the question: ${prompt}`;
    } else {
      prompt = `Answer the following question about ${title || 'market research'} with detailed, helpful information: ${prompt}`;
    }
    
    try {
      const response = await openai.chat.completions.create({
        model: model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 1500
      });
      
      console.log('OpenAI direct response:', response);
      return response.choices[0].message.content;
    } catch (error) {
      console.error('Error calling OpenAI directly:', error);
      throw error;
    }
  };

  const handleAskQuestion = async (e) => {
    e.preventDefault();
    
    if (!question.trim()) return;
    
    setLoading(true);
    setAnswer('Processing your question...');
    setLastQuestion(question); // Save the current question
    
    try {
      // Determine which API endpoint to use based on the selected model
      const isOpenAI = selectedModel.startsWith('gpt') || selectedModel.startsWith('o1-');
      const isDeepSeek = selectedModel.startsWith('deepseek');
      const isAnthropic = selectedModel.startsWith('claude');
      const isGroq = selectedModel.includes('llama') || selectedModel.includes('mixtral') || selectedModel.includes('groq');

      let apiEndpoint;
      let apiProvider;

      if (isOpenAI) {
        apiEndpoint = config.endpoints.openai.ask;
        apiProvider = 'OpenAI';
      } else if (isDeepSeek) {
        apiEndpoint = config.endpoints.deepseek.ask;
        apiProvider = 'DeepSeek';
      } else if (isAnthropic) {
        apiEndpoint = config.endpoints.anthropic.ask;
        apiProvider = 'Anthropic';
      } else if (isGroq) {
        apiEndpoint = config.endpoints.groq.ask;
        apiProvider = 'Groq';
      } else {
        apiEndpoint = config.endpoints.gemini.ask;
        apiProvider = 'Gemini';
      }

      console.log(`=== ${apiProvider} API REQUEST LOG ===`);
      console.log(`Sending question to ${apiProvider} API:`, question);
      console.log('API URL:', apiEndpoint);
      console.log('Railway API Base URL:', config.RAILWAY_API_BASE_URL);
      console.log('Selected model:', selectedModel);
      console.log('============================');
      
      // Check if the server is running
      let serverRunning = false;
      let useServerResponse = true;
      let responseData = null;
      
      try {
        // Try a quick ping to the server
        const pingResponse = await fetch(`${config.API_BASE_URL}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          // Set a short timeout for the ping
          signal: AbortSignal.timeout(3000)
        });
        serverRunning = pingResponse.ok;
        console.log('Server ping status:', serverRunning ? 'Server is running' : 'Server not responding properly');
      } catch (pingError) {
        console.error('Server ping failed:', pingError);
        // Continue anyway, the main request will handle errors
      }
      
      // Try to call the Railway API first
      try {
        const response = await fetch(apiEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: contextPrompt ? `${contextPrompt}\n\nQuestion: ${question}` : question,
            question: contextPrompt ? `${contextPrompt}\n\nQuestion: ${question}` : question,
            model: selectedModel,
            thinkingMode: thinkingMode
          }),
        });
        
        console.log('Response status:', response.status);
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        if (response.status === 200) {
          // Parse the Railway backend response
          responseData = JSON.parse(responseText);
          console.log('Parsed Railway response:', responseData);
        } else {
          throw new Error(`Railway API error: ${response.status} - ${responseText}`);
        }
      } catch (error) {
        console.error('Error fetching from server:', error);
        
        // Try client-side OpenAI if it's an OpenAI model
        if (isOpenAI) {
          console.log('Server API call failed. Trying client-side OpenAI API...');
          useServerResponse = false;
          
          // If we don't have an API key yet, show the dialog and return early
          if (!openaiApiKey) {
            setShowApiKeyDialog(true);
            setLoading(false);
            setAnswer('Please enter your OpenAI API key to continue.');
            return;
          }
          
          try {
            // Call OpenAI directly from the client
            const directResponse = await callOpenAIDirectly(question, selectedModel, thinkingMode);
            responseData = { answer: directResponse, model: selectedModel, thinkingMode };
          } catch (openaiError) {
            console.error('Error in client-side OpenAI call:', openaiError);
            // Provide a fallback response
            provideFallbackResponse();
            return;
          }
        } else {
          // For non-OpenAI models, provide a fallback response
          provideFallbackResponse();
          return;
        }
      }
      
      // Process the response data from Railway backend
      if (responseData) {
        // Extract the answer from Railway backend response format
        let finalAnswer = '';

        if (responseData.text) {
          finalAnswer = responseData.text;
        } else if (responseData.answer) {
          finalAnswer = responseData.answer;
        } else if (responseData.response) {
          finalAnswer = responseData.response;
        } else {
          throw new Error('No valid response text found');
        }

        setAnswer(finalAnswer);
        setIsAIResponse(true);
        // Store the model information
        setCurrentModel(responseData.model || selectedModel);
        // Clear the question input after getting a response
        setQuestion('');
        console.log('=== RAILWAY API RESPONSE LOG ===');
        console.log('Successfully received response from Railway');
        console.log('Provider:', apiProvider);
        console.log('Model used:', responseData.model || selectedModel);
        console.log('Thinking mode:', responseData.thinkingMode ? 'Enabled' : 'Disabled');
        console.log('Response length:', finalAnswer.length);
        console.log('Response preview:', finalAnswer.substring(0, 100) + '...');
        if (responseData.mock) {
          console.log('⚠️ Mock response (no API key configured on Railway)');
        }
        console.log('==============================');
      } else {
        throw new Error('No response data received from Railway');
      }
    } catch (error) {
      console.error('Error processing question:', error);
      provideFallbackResponse();
    } finally {
      setLoading(false);
    }
  };
  
  // Provide a fallback response when the API is rate limited
  const provideFallbackResponse = () => {
    console.log('Providing fallback response due to error or rate limiting');
    
    const fallbackResponse = `
    I apologize, but I'm currently unable to process your request due to one of the following reasons:
    
    1. The API server may be experiencing high traffic or rate limiting
    2. There might be a temporary connection issue
    3. The selected model might be unavailable at the moment
    
    Here are some general tips for ${title || 'market research'}:
    
    * Gather both quantitative and qualitative data from diverse sources
    * Analyze customer demographics, preferences, and behaviors
    * Study market trends, size, and growth potential
    * Examine competitors' strengths and weaknesses
    * Test your assumptions with small-scale experiments
    * Regularly update your research to stay current
    
    Please try again in a few moments or select a different model.
    `;
    
    setAnswer(fallbackResponse);
    setIsAIResponse(false);
    setLoading(false);
    console.log('Fallback response length:', fallbackResponse.length);
  };
  
  // OpenAI API Key Dialog
  const renderApiKeyDialog = () => {
    if (!showApiKeyDialog) return null;
    
    return (
      <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg shadow-xl w-96">
          <h2 className="text-xl font-semibold mb-4">Enter OpenAI API Key</h2>
          <p className="mb-4 text-sm text-gray-600">
            To use OpenAI models directly from your browser, please enter your OpenAI API key.
            Your key will only be stored in your browser for this session and will not be saved permanently.
          </p>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="apiKey">
              API Key:
            </label>
            <input
              id="apiKey"
              type="password"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={apiKeyInputValue}
              onChange={(e) => {
                setApiKeyInputValue(e.target.value);
                setApiKeyError(null);
              }}
              placeholder="sk-..."
            />
            {apiKeyError && <p className="text-red-500 text-xs mt-1">{apiKeyError}</p>}
            <p className="text-xs text-gray-500 mt-1">Your key should start with "sk-"</p>
          </div>
          
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => {
                setShowApiKeyDialog(false);
                setAnswer('');
              }}
              className="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded transition duration-200"
            >
              Cancel
            </button>
            
            <button
              onClick={() => {
                if (apiKeyInputValue.trim().startsWith('sk-')) {
                  setOpenaiApiKey(apiKeyInputValue.trim());
                  setShowApiKeyDialog(false);
                  // Re-trigger the question submission
                  if (lastQuestion) {
                    const event = { preventDefault: () => {} };
                    handleAskQuestion(event);
                  }
                } else {
                  setApiKeyError('Please enter a valid OpenAI API key starting with "sk-"');
                }
              }}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition duration-200"
              disabled={!apiKeyInputValue.trim().startsWith('sk-')}
            >
              Save & Continue
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md">
      {renderApiKeyDialog()}
      <h2 className="raleway-title-h2 mb-4 text-center">{title || 'AI Assistant'}</h2>
      <p className="mb-6 text-center">{description || 'Ask questions and get AI-powered answers.'}</p>
      
      {/* Agent response section - above the input box */}
      {answer && (
        <div className="mt-6 p-4 bg-gray-100 rounded mb-6">
          {lastQuestion && (
            <div className="mb-4">
              <h3 className="font-bold mb-1">Question:</h3>
              <p className="italic text-gray-700 pl-2 border-l-2 border-gray-400">{lastQuestion}</p>
            </div>
          )}
          <h3 className="font-bold mb-2">Answer:</h3>
          <div 
            className="whitespace-pre-wrap text-left"
            dangerouslySetInnerHTML={{ __html: formatAnswerWithBoldTitles(answer) }}
          />
          {!isAIResponse && answer.includes('fallback response') && (
            <p className="mt-2 text-sm text-red-500">
              Using fallback response due to API rate limits
            </p>
          )}
          <div className="mt-4 flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => {
                navigator.clipboard.writeText(answer);
                const btn = document.activeElement;
                if (btn) {
                  const originalText = btn.textContent;
                  btn.textContent = 'Copied!';
                  btn.classList.add('bg-green-200', 'text-green-800');
                  setTimeout(() => {
                    btn.textContent = originalText;
                    btn.classList.remove('bg-green-200', 'text-green-800');
                  }, 1500);
                }
              }}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 text-sm flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
              </svg>
              Copy to Clipboard
            </button>
            
            <button
              type="button"
              onClick={() => {
                setShowSaveDialog(true);
                setDocumentName(`${title || 'AI'}_Response_${new Date().toLocaleDateString().replace(/\//g, '-')}`);
              }}
              className="px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 text-sm flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              Save as Document
            </button>
          </div>
        </div>
      )}
      
      {/* Question input form */}
      <form onSubmit={handleAskQuestion} className="mt-6">
        <div className="flex">
          <input
            type="text"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            placeholder={`Ask a question about ${title || 'market research'}...`}
            className="flex-grow p-2 border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading}
          />
          <button 
            type="submit" 
            className="bg-blue-600 text-white px-4 py-2 rounded-r-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300"
            disabled={loading || !question.trim()}
          >
            {loading ? 'Asking...' : 'Ask'}
          </button>
        </div>
        
        {/* Model selection and thinking mode controls */}
        <div className="mt-4 flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 md:space-x-4">
          <div className="flex items-center mr-4">
            <label htmlFor="model-select" className="text-sm mr-2 text-gray-600">Model:</label>
            <select
              id="model-select"
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              className="text-sm border rounded p-1 bg-white"
              disabled={loading}
            >
              {Object.entries(modelOptions).map(([provider, models]) => (
                <optgroup key={provider} label={provider}>
                  {models.map(model => (
                    <option key={model.value} value={model.value}>
                      {model.label}
                    </option>
                  ))}
                </optgroup>
              ))}
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="thinkingMode"
              checked={thinkingMode}
              onChange={(e) => setThinkingMode(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={loading}
            />
            <label htmlFor="thinkingMode" className="text-sm font-medium text-gray-700">
              Enable Thinking Mode
            </label>
          </div>
        </div>
        
        {/* Display current model if a response has been received */}
        {isAIResponse && currentModel && (
          <div className="mt-2 text-xs text-gray-500">
            Response generated using: {currentModel} {thinkingMode ? 'with thinking mode' : ''}
          </div>
        )}
      </form>
      
      {/* Save Document Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-96">
            <h2 className="text-xl font-semibold mb-4">Download Answer as Document</h2>
            
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="documentName">
                Document Name:
              </label>
              <input
                id="documentName"
                type="text"
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value={documentName}
                onChange={(e) => setDocumentName(e.target.value)}
                placeholder="Enter document name"
              />
              <p className="text-sm text-gray-600 mt-2">The document will be downloaded as a Markdown (.md) file</p>
            </div>
            
            <div className="flex justify-end space-x-2">
              <button
                className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                onClick={() => setShowSaveDialog(false)}
              >
                Cancel
              </button>
              <button
                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                onClick={handleSaveDocument}
              >
                Download Document
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
