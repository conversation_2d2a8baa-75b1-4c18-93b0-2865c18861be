@tailwind base;
@tailwind components;
@tailwind utilities; 



/* styles.css: Custom global styles for Omega Praxis */

/* Gold color utility */
.gold {
  color: #D4AF37 !important;
}

/* Omega Praxis Navigation Button - Gold Theme */
.omega-nav-btn {
  color: #b0a989 !important;
  border: 2px solid #b0a989 !important;
  background: #fff !important;
  border-radius: 10px !important;
  font-weight: 600 !important;
  font-family: 'Inter', Arial, sans-serif !important;
  font-size: 1rem !important;
  padding: 10px 32px !important;
  box-shadow: none !important;
  transition: background 0.2s, color 0.2s, border 0.2s;
  outline: none !important;
}
.omega-nav-btn:hover:not(:disabled), .omega-nav-btn:focus:not(:disabled) {
  background: #faf6f1 !important;
  color: #9c8a5c !important;
  border-color: #9c8a5c !important;
}
.omega-nav-btn--disabled,
.omega-nav-btn:disabled {
  background: #f4f1ea !important;
  color: #c8bfa3 !important;
  border-color: #e6e1d3 !important;
  cursor: not-allowed !important;
}

/* Gold background utility (if needed) */
.bg-gold {
  background-color: #D4AF37 !important;
}

/* Uppercase and Inter font utility */
.upper-inter {
  text-transform: uppercase !important;
  font-family: 'Inter', sans-serif !important;
}

/* Thin minimalist heading utility for Raleway */
.raleway-title {
  font-family: 'Raleway', Arial, sans-serif !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.25em !important;
  color: #D4AF37 !important;
}




@media (min-width: 768px) { /* LOGO */
  .raleway-title {
    font-size: 2.2rem !important;
    padding-top: 0.625rem !important; 
  }
}

/* Minimalist menu item utility for Raleway */
.raleway-menu {
  font-family: 'Raleway', Arial, sans-serif !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0em !important;
  color: #948979 !important;
  font-size: 0.92rem !important;
}

/* Thinner and smaller body text with almost black color using Montserrat Light 300 */
p, li, .body-text {
  font-family: 'Montserrat', Arial, sans-serif !important;
  font-weight: 300 !important;
  letter-spacing: 0.01em;
  color: #604c28 !important;
  font-size: 14px !important;
  
}

.bg-beige {
  background-color: #FAF6F1 !important;
}

.booknow-btn {
  background-color: #fff !important;
  color: #2563eb !important;
  border: 0.5px solid rgba(37,99,235,0.2) !important;
  text-transform: uppercase !important;
  transition: background 0.2s, color 0.2s, border 0.2s;
  padding-top: 0.3125rem !important; /* 5px */
  padding-bottom: 0.3125rem !important; /* 5px */
  font-size: 0.92rem !important;
}
.booknow-btn:hover, .booknow-btn:focus {
  background-color: #e6f0fd !important;
  color: #2563eb !important;
  border-color: #2563eb !important;
}

.logo h1 {
  font-size: 2.5rem !important;
  line-height: 1.1 !important;
  /* color: #948979 !important; */
}
@media (min-width: 768px) {
  .logo h1 {
    font-size: 4rem !important;
  }
}

.raleway-title-h0 {
  font-family: 'Raleway', Arial, sans-serif !important;
  font-weight: 500 !important;
  margin-bottom: 18px;
  text-transform: uppercase !important;
  letter-spacing: 0.25em !important;
  color: #D4AF37 !important;
  font-size: 2rem !important;
}

.raleway-title-h1 {
  font-family: 'Raleway', Arial, sans-serif !important;
  font-weight: 400 !important;
  margin-bottom: 18px;
  text-transform: uppercase !important;
  letter-spacing: 0.10em !important;
  color: #2563eb !important;
  font-size: 1.2rem !important;
}



.raleway-title-h2 {
  font-family: 'Raleway', Arial, sans-serif !important;
  font-weight: 400 !important;
  margin-bottom: 18px;
  text-transform: uppercase !important;
  letter-spacing: 0.10em !important;
  color: #2563eb !important;
  font-size: 1.6rem !important;
}


.raleway-title-h3 {
  font-family: 'Raleway', Arial, sans-serif;
  font-weight: 400;
  font-size: 1.1em !important;
  text-transform: uppercase !important;
  color: #2563eb;
  text-transform: none;
  letter-spacing: 0.10em;
  margin-bottom: 18px;
}
.raleway-title-h4 {
  font-family: 'Raleway', Arial, sans-serif;
  font-weight: 400;
  text-transform: uppercase !important;
  color: #2563eb;
  text-transform: none;
  letter-spacing: 0em;
  font-size: 1rem !important;
}

strong {
  font-weight: 500;
  font-size: 20px;
}

.bold-heavy {
  font-weight: 800;
}

.omega-nav-btn {
  background-color: #f8fafc !important;
  color: #2563eb !important;
  border: 0.5px solid rgba(37,99,235,0.2) !important;
  text-transform: uppercase !important;
  transition: background 0.2s, color 0.2s, border 0.2s;
  padding-top: 0.3125rem !important; /* 5px */
  padding-bottom: 0.3125rem !important; /* 5px */
  padding-left: 0.625rem !important; /* 10px */
  padding-right: 0.625rem !important; /* 10px */
  font-size: 0.75rem !important;
  border-radius: 0.25rem !important;
}

/* Button styles for feature cards and section headers */
.feature-btn {
  background-color: #ffffff !important; /* White */
  color: #0369a1 !important;
  border: 1px solid #0ea5e9 !important;
  text-transform: uppercase !important;
  transition: all 0.2s ease;
  padding: 0.5rem 1rem !important;
  font-size: 0.85rem !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.feature-btn:hover {
  background-color: #f0f9ff !important; /* Very light blue */
  color: #0c4a6e !important;
}

/* Questionnaire button style */
.questionnaire-btn {
  background-color: #f0fdf4 !important; /* Light green */
  color: #166534 !important;
  border: 1px solid #22c55e !important;
  text-transform: uppercase !important;
  transition: all 0.2s ease;
  padding: 0.5rem 1.25rem !important;
  font-size: 0.85rem !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.questionnaire-btn:hover {
  background-color: #dcfce7 !important;
  color: #14532d !important;
}

