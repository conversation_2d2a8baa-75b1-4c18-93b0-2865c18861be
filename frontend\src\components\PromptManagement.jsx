import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import config from '../config';
import ReactMarkdown from 'react-markdown';
import { Tab, Tabs, Tab<PERSON>ist, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';

// Default prompts from the application
const defaultPrompts = {
  marketingStrategy: {
    name: "Marketing Strategy",
    prompt: `Generate a comprehensive marketing strategy based on the following questionnaire responses: [RESPONSES]. 

COMPANY PROFILE:
[COMPANY_PROFILE]

You are a world-class marketing strategist with expertise in developing effective marketing plans across various industries. Create a detailed marketing strategy that addresses the specific needs, challenges, and opportunities identified in the questionnaire responses. Tailor your strategy specifically to the company profile information provided above.

Your strategy should include the following sections, each with detailed and actionable content:
1. Executive Summary - A concise overview of the entire strategy
2. Market Analysis - Industry trends, competitive landscape, and market opportunities
3. Target Audience - Detailed customer personas with demographics, psychographics, and behavioral patterns
4. Positioning Strategy - Unique value proposition and brand positioning
5. Marketing Channels - Specific channels to reach the target audience with budget allocation percentages
6. Messaging Framework - Key messages, tone, and communication style
7. Content Strategy - Types of content to create for each stage of the customer journey
8. Budget Allocation - Detailed breakdown of marketing spend across channels and initiatives
9. Timeline - Phased approach with key milestones
10. Success Metrics - KPIs and measurement framework

Format your response using markdown with clear headings, bullet points, and numbered lists where appropriate. Be specific, practical, and data-driven in your recommendations.`,
    description: "Used to generate the main marketing strategy"
  },
  implementationPlan: {
    name: "Implementation Plan",
    prompt: `Based on the following questionnaire responses: [RESPONSES],

COMPANY PROFILE:
[COMPANY_PROFILE]

You are an experienced marketing implementation specialist. Create a detailed, actionable implementation plan for executing the marketing strategy that addresses the specific needs and challenges identified in the questionnaire responses. Tailor your implementation plan specifically to the company profile information provided above.

Your implementation plan should include:

1. Immediate Actions (Next 30 Days)
   - List specific tasks with owners, deadlines, and required resources
   - Include quick wins that can show immediate results

2. Short-term Actions (1-3 Months)
   - Key initiatives to build momentum
   - Required team structure and responsibilities
   - Technology and tools needed

3. Medium-term Actions (3-6 Months)
   - Scaling successful initiatives
   - Performance review processes
   - Optimization strategies

4. Long-term Actions (6-12 Months)
   - Strategic expansion opportunities
   - Advanced measurement and analytics implementation

For each phase, include:
- Specific tasks and subtasks
- Resource requirements (people, budget, tools)
- Dependencies between tasks
- Risk factors and mitigation strategies
- Success criteria for each milestone

Format your response using markdown with clear headings, numbered lists, and bullet points for readability. Be practical, specific, and focused on execution.`,
    description: "Used to generate the implementation plan section"
  },
  recommendations: {
    name: "Recommendations",
    prompt: `Based on the following questionnaire responses: [RESPONSES],

COMPANY PROFILE:
[COMPANY_PROFILE]

You are a strategic marketing advisor with expertise in optimizing marketing performance. Provide specific, actionable recommendations to improve marketing effectiveness based on the questionnaire responses. Tailor your recommendations specifically to the company profile information provided above.

Your recommendations should cover:

1. Key Messaging and Positioning
   - Specific messaging frameworks that will resonate with the target audience
   - Positioning statements that differentiate from competitors
   - Tone and voice guidelines

2. High-Impact Marketing Channels
   - Prioritized list of channels with expected ROI
   - Channel-specific optimization strategies
   - Cross-channel integration opportunities

3. Content Strategy Recommendations
   - Content types and formats for each stage of the buyer's journey
   - Content themes and topics that will engage the target audience
   - Content distribution and promotion strategies

4. Budget Optimization
   - Areas where spending can be reduced without impacting results
   - High-ROI opportunities that deserve additional investment
   - Resource allocation recommendations

5. Technology and Tools
   - Recommended marketing technology stack
   - Integration strategies for existing systems
   - Implementation priorities and timeline

For each recommendation:
- Explain the rationale and expected benefits
- Provide implementation guidance
- Suggest metrics to track effectiveness

Format your response using markdown with clear headings, bullet points, and numbered lists for readability. Follow these guidelines:

- Provide detailed, actionable, and data-driven marketing advice
- Tailor your recommendations to the specific industry, target audience, and business goals
- Base recommendations on data and insights from the questionnaire responses
- Prioritize recommendations based on potential impact and feasibility
- Consider both short-term tactics and long-term strategic initiatives

Please provide a helpful response to the following question: [QUESTION]`,
    description: "Used to generate the recommendations section"
  },
  geminiPrompt: {
    name: "Gemini System Prompt",
    prompt: `You are an expert marketing strategist and consultant with deep expertise in developing effective marketing strategies across various industries.

- Provide detailed, actionable, and data-driven marketing advice
- Tailor your recommendations to the specific industry, target audience, and business goals
- Base recommendations on data and insights from the questionnaire responses
- Prioritize recommendations based on potential impact and feasibility
- Consider both short-term tactics and long-term strategic initiatives

Please provide a helpful response to the following question: [QUESTION]`,
    description: "System prompt used for Gemini API calls"
  },
  geminiThinkingPrompt: {
    name: "Gemini Thinking Prompt",
    prompt: `You are an expert marketing strategist and consultant with deep expertise in developing effective marketing strategies across various industries. Please think through this problem step by step:

1. THINKING: First, analyze the question and relevant information. Consider different angles, potential approaches, and important factors. Think through your reasoning carefully.

2. ANSWER: Then provide your final, well-structured answer based on your thinking. Include specific, actionable recommendations with clear rationales.

Question: [QUESTION]`,
    description: "System prompt with thinking mode for Gemini API calls"
  }
};

export default function PromptManagement() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [prompts, setPrompts] = useState(defaultPrompts);
  const [activePrompt, setActivePrompt] = useState('marketingStrategy');
  const [editedPrompt, setEditedPrompt] = useState('');
  const [testData, setTestData] = useState('{' +
    '"responses": {' +
      '"industry": "Technology",' +
      '"target_audience": "Small business owners",' +
      '"budget": "$10,000 per month",' +
      '"timeline": "6 months",' +
      '"goals": "Increase brand awareness and generate leads",' +
      '"challenges": "High competition and limited brand recognition"' +
    '},' +
    '"company_profile": {' +
      '"name": "TechSolutions Inc.",' +
      '"industry": "Software Development",' +
      '"size": "50-100 employees",' +
      '"founded": "2015",' +
      '"location": "San Francisco, CA",' +
      '"mission": "To simplify technology for small businesses",' +
      '"vision": "A world where every small business has access to enterprise-grade technology",' +
      '"values": "Innovation, Accessibility, Customer Success",' +
      '"targetAudience": "Small to medium businesses in retail and service industries",' +
      '"uniqueSellingPoints": "Affordable pricing, intuitive interfaces, 24/7 customer support",' +
      '"competitors": "Enterprise Solutions, TechGiant, SmallBizTech",' +
      '"challenges": "Market saturation, educating customers about technology benefits",' +
      '"goals": "Expand market share by 15% in the next year, launch 3 new product features"' +
    '}' +
  '}');
  const [testResponse, setTestResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [companyProfile, setCompanyProfile] = useState(null);

  // Load custom prompts from localStorage
  const loadPrompts = () => {
    const savedPrompts = localStorage.getItem('customPrompts');
    if (savedPrompts) {
      try {
        const parsedPrompts = JSON.parse(savedPrompts);
        // Merge with default prompts to ensure we have all the latest defaults
        const mergedPrompts = { ...defaultPrompts };
        
        // Only override prompts that exist in the defaults
        Object.keys(parsedPrompts).forEach(key => {
          if (mergedPrompts[key]) {
            mergedPrompts[key] = parsedPrompts[key];
          }
        });
        
        setPrompts(mergedPrompts);
      } catch (error) {
        console.error('Error parsing saved prompts:', error);
      }
    }
  };

  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    
    // Load prompts
    loadPrompts();
    
    // Load company profile
    const savedProfile = localStorage.getItem('companyProfile');
    if (savedProfile) {
      setCompanyProfile(JSON.parse(savedProfile));
    }
  }, [user, navigate]);

  // Load custom prompts from localStorage on component mount
  useEffect(() => {
    loadPrompts();
  }, []);

  // Set edited prompt when active prompt changes
  useEffect(() => {
    if (prompts[activePrompt]) {
      setEditedPrompt(prompts[activePrompt].prompt);
    }
  }, [activePrompt, prompts]);

  const handlePromptSelect = (promptKey) => {
    setActivePrompt(promptKey);
  };

  const handlePromptChange = (e) => {
    setEditedPrompt(e.target.value);
  };

  const handleSavePrompt = () => {
    try {
      const updatedPrompts = {
        ...prompts,
        [activePrompt]: {
          ...prompts[activePrompt],
          prompt: editedPrompt
        }
      };
      
      setPrompts(updatedPrompts);
      localStorage.setItem('customPrompts', JSON.stringify(updatedPrompts));
      alert('Prompt saved successfully!');
    } catch (error) {
      console.error('Error saving prompt:', error);
      alert('Failed to save prompt. Please try again.');
    }
  };

  const handleResetPrompt = () => {
    if (window.confirm('Are you sure you want to reset this prompt to its default value?')) {
      if (defaultPrompts[activePrompt]) {
        const updatedPrompts = {
          ...prompts,
          [activePrompt]: {
            ...defaultPrompts[activePrompt]
          }
        };
        
        setPrompts(updatedPrompts);
        setEditedPrompt(defaultPrompts[activePrompt].prompt);
        localStorage.setItem('customPrompts', JSON.stringify(updatedPrompts));
        alert('Prompt reset to default.');
      }
    }
  };

  const handleTestDataChange = (e) => {
    setTestData(e.target.value);
  };

  const handleTestPrompt = async () => {
    setIsLoading(true);
    setTestResponse('');
    
    // Define processedPrompt at a higher scope so it's available in the catch block
    let processedPrompt = '';
    
    try {
      let parsedTestData;
      try {
        parsedTestData = JSON.parse(testData);
      } catch (error) {
        alert('Invalid JSON in test data. Please check your format.');
        setIsLoading(false);
        return;
      }
      
      // Extract responses and company profile from the test data
      const responsesData = parsedTestData.responses || parsedTestData;
      const companyProfileData = parsedTestData.company_profile;
      
      // Replace [RESPONSES] with just the responses part of the data
      processedPrompt = editedPrompt
        .replace('[RESPONSES]', JSON.stringify(responsesData))
        .replace('[QUESTION]', JSON.stringify(responsesData));
      
      console.log('Sending prompt to API:', processedPrompt);
      
      // Add company profile if available in test data or from the app state
      if (processedPrompt.includes('[COMPANY_PROFILE]')) {
        let profileToUse = companyProfileData || companyProfile;
        
        if (profileToUse) {
          const formattedProfile = Object.entries(profileToUse)
            .map(([key, value]) => {
              // Format the key to be more readable
              const formattedKey = key.replace(/([A-Z])/g, ' $1')
                .replace(/^./, str => str.toUpperCase())
                .replace(/([a-z])([A-Z])/g, '$1 $2');
              
              return `${formattedKey}: ${value}`;
            })
            .join('\n');
          
          processedPrompt = processedPrompt.replace('[COMPANY_PROFILE]', formattedProfile);
        } else {
          processedPrompt = processedPrompt.replace('[COMPANY_PROFILE]', 'No company profile information available.');
        }
      }
      
      const response = await axios.post(config.endpoints.gemini.ask, {
        prompt: processedPrompt, // Changed from 'question' to 'prompt'
        model: 'gemini-1.5-pro',
        thinkingMode: activePrompt.includes('Thinking')
      });
      
      console.log('API response:', response.data);
      setTestResponse(response.data.answer);
    } catch (error) {
      console.error('Error testing prompt:', error);
      setTestResponse(`Error: ${error.message || 'Failed to generate response'}`);
      
      // Try direct API call to Gemini if the backend API server is not running
      try {
        // Use the API key from the .env file
        const apiKey = 'AIzaSyDxukBSjF4sJkULasg_YLzO0YAE-hUdWlw';
        
        if (!apiKey) {
          setTestResponse('# API Key Required\n\nNo Gemini API key found. Please add your API key to the .env file.');
          return;
        }
        
        console.log('Making direct call to Gemini API with prompt:', processedPrompt);
        
        // Make a direct call to Gemini API
        const directResponse = await axios.post('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent', {
          contents: [{
            parts: [{
              text: processedPrompt
            }]
          }]
        }, {
          params: {
            key: apiKey
          },
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        console.log('Direct Gemini API response:', directResponse);
        
        // Extract the response text from Gemini API
        console.log('Parsing Gemini API response:', JSON.stringify(directResponse.data));
        
        if (directResponse.data && directResponse.data.candidates && directResponse.data.candidates.length > 0) {
          const candidate = directResponse.data.candidates[0];
          
          if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
            const text = candidate.content.parts[0].text;
            console.log('Successfully extracted text from Gemini API response');
            setTestResponse(text);
          } else {
            console.error('Could not find text in Gemini API response parts');
            setTestResponse('# Error\n\nCould not extract text from Gemini API response. Check console for details.');
          }
        } else {
          console.error('Unexpected Gemini API response format');
          setTestResponse('# Error\n\nReceived an unexpected response format from Gemini API. Check console for details.');
        }
      } catch (directError) {
        console.error('Direct Gemini API call failed:', directError);
        setTestResponse(`# API Error\n\nCould not connect to Gemini API: ${directError.message}\n\nPlease check your API key and internet connection.`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
        <h2 className="raleway-title-h2 mb-4">Prompts</h2>
        <p className="body-text mb-6">
          View and edit the prompts used to generate marketing strategies and other content in the application.
          Changes you make here will be saved to your browser and used for future generations.
        </p>

        {/* Horizontal Prompt Templates Menu */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
          {Object.keys(prompts).map((promptKey) => (
            <button
              key={promptKey}
              className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all ${activePrompt === promptKey ? 'bg-pink-100 border-pink-300 text-pink-800' : 'bg-pink-50 hover:bg-pink-100 text-pink-700'}`}
              onClick={() => handlePromptSelect(promptKey)}
            >
              <h3 className="raleway-title-h3 text-sm">{prompts[promptKey].name}</h3>
            </button>
          ))}
        </div>
      </div>

      {/* Main Content Area */}
      {!companyProfile && (
        <div className="w-full max-w-4xl mx-auto">
          <div className="bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700 p-4 mb-6 rounded">
            <p className="raleway-title-h3 mb-2">Company Profile Not Found</p>
            <p className="body-text">
              To enhance AI responses with your company context, please
              <Link to="/company-profile" className="text-blue-600 underline">complete your company profile</Link>.
            </p>
          </div>
        </div>
      )}

      {prompts[activePrompt] && (
        <div className="w-full max-w-4xl mx-auto space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="raleway-title-h2 mb-2 text-center">{prompts[activePrompt].name}</h2>
            <p className="body-text mb-4 text-center text-gray-600">{prompts[activePrompt].description}</p>

              <div className="mb-4">
                <label className="block raleway-title-h3 text-gray-700 mb-2">
                  Edit Prompt:
                </label>
                <textarea
                  className="w-full h-64 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                  value={editedPrompt}
                  onChange={handlePromptChange}
                />
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  className="px-6 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-pink-500 transition"
                  onClick={handleSavePrompt}
                >
                  Save Changes
                </button>
                <button
                  className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 transition"
                  onClick={handleResetPrompt}
                >
                  Reset to Default
                </button>
              </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="raleway-title-h2 mb-4 text-center">Test Prompt</h3>
              <Tabs>
                <TabList>
                  <Tab>Input</Tab>
                  <Tab>Output</Tab>
                </TabList>
                
                <TabPanel>
                  <div className="mb-4">
                    <textarea
                      className="w-full h-48 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                      value={testData}
                      onChange={handleTestDataChange}
                      placeholder="Enter test data in JSON format..."
                    />
                  </div>
                  <div className="text-center">
                    <button
                      className="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-400 transition"
                      onClick={handleTestPrompt}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Testing...' : 'Test Prompt'}
                    </button>
                  </div>
                </TabPanel>
                
                <TabPanel>
                  <div className="border border-gray-300 rounded-lg p-4 min-h-64 bg-beige">
                    {testResponse ? (
                      <div className="prose max-w-none response-content">
                        <style jsx>{`
                          .response-content :global(h1) {
                            font-family: 'Raleway', Arial, sans-serif !important;
                            font-weight: 300 !important;
                            margin-top: 32px !important;
                            margin-bottom: 24px !important;
                            padding-top: 16px !important;
                            text-transform: uppercase !important;
                            letter-spacing: 0.10em !important;
                            color: #2563eb !important;
                            font-size: 2rem !important;
                          }
                          
                          .response-content :global(h2) {
                            font-family: 'Raleway', Arial, sans-serif !important;
                            font-weight: 400 !important;
                            margin-top: 28px !important;
                            margin-bottom: 20px !important;
                            padding-top: 12px !important;
                            text-transform: uppercase !important;
                            letter-spacing: 0.10em !important;
                            color: #2563eb !important;
                            font-size: 1.6rem !important;
                          }
                          
                          .response-content :global(h3) {
                            font-family: 'Raleway', Arial, sans-serif !important;
                            font-weight: 400 !important;
                            margin-top: 24px !important;
                            margin-bottom: 16px !important;
                            padding-top: 8px !important;
                            text-transform: uppercase !important;
                            letter-spacing: 0.10em !important;
                            color: #2563eb !important;
                            font-size: 1.2rem !important;
                          }
                          
                          .response-content :global(p), .response-content :global(li) {
                            font-family: 'Montserrat', Arial, sans-serif !important;
                            font-weight: 300 !important;
                            letter-spacing: 0.01em;
                            color: #604c28 !important;
                            font-size: 14px !important;
                            line-height: 1.6 !important;
                            margin-bottom: 16px !important;
                          }
                          
                          .response-content :global(ul), .response-content :global(ol) {
                            padding-left: 24px !important;
                            margin-bottom: 20px !important;
                            margin-top: 12px !important;
                          }
                          
                          .response-content :global(li) {
                            margin-bottom: 8px !important;
                          }
                          
                          .response-content :global(strong) {
                            font-weight: 500 !important;
                            color: #483c1f !important;
                          }
                          
                          .response-content :global(blockquote) {
                            border-left: 4px solid #2563eb !important;
                            padding-left: 16px !important;
                            margin-left: 0 !important;
                            margin-right: 0 !important;
                            font-style: italic !important;
                            color: #604c28 !important;
                            background-color: rgba(37, 99, 235, 0.05) !important;
                            padding: 16px !important;
                            border-radius: 0 4px 4px 0 !important;
                            margin-bottom: 20px !important;
                          }
                          
                          .response-content :global(hr) {
                            border: none !important;
                            height: 1px !important;
                            background-color: rgba(96, 76, 40, 0.2) !important;
                            margin: 32px 0 !important;
                          }
                          
                          .response-content :global(table) {
                            width: 100% !important;
                            border-collapse: collapse !important;
                            margin-bottom: 24px !important;
                            font-size: 14px !important;
                          }
                          
                          .response-content :global(th), .response-content :global(td) {
                            border: 1px solid rgba(96, 76, 40, 0.2) !important;
                            padding: 8px 12px !important;
                            text-align: left !important;
                          }
                          
                          .response-content :global(th) {
                            background-color: rgba(37, 99, 235, 0.1) !important;
                            font-weight: 500 !important;
                            color: #2563eb !important;
                          }
                        `}</style>
                        <ReactMarkdown>{testResponse}</ReactMarkdown>
                      </div>
                    ) : (
                      <p className="text-gray-500 italic body-text text-center">
                        {isLoading ? 'Generating response...' : 'Test a prompt to see the response here'}
                      </p>
                    )}
                  </div>
                </TabPanel>
              </Tabs>
          </div>
        </div>
      )}
    </div>
  );
}
