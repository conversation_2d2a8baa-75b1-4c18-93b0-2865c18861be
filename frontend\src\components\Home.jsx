import React from 'react';
import { Link } from 'react-router-dom';

function Home() {
  return (
    <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center">
  
      <p className="body-text mb-6">
        Your comprehensive marketing research platform designed to help businesses make data-driven decisions.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8 mb-8">
        <Link to="/market-research" className="no-underline">
          <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow text-center bg-green-50 hover:bg-green-100 h-32 flex flex-col justify-center">
            <h3 className="raleway-title-h3 mb-2 text-green-700">Market Research</h3>
            <p className="body-text text-gray-700">Understand your target market and customer needs.</p>
          </div>
        </Link>

        <Link to="/competition-analysis" className="no-underline">
          <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow text-center bg-purple-50 hover:bg-purple-100 h-32 flex flex-col justify-center">
            <h3 className="raleway-title-h3 mb-2 text-purple-700">Competition Analysis</h3>
            <p className="body-text text-gray-700">Analyze competitors and identify your competitive advantages.</p>
          </div>
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <Link to="/client-acquisition" className="no-underline">
          <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow text-center bg-orange-50 hover:bg-orange-100 h-32 flex flex-col justify-center">
            <h3 className="raleway-title-h3 mb-2 text-orange-700">Client Acquisition</h3>
            <p className="body-text text-gray-700">Strategies and tools to attract new clients to your business.</p>
          </div>
        </Link>

        <Link to="/customer-retention" className="no-underline">
          <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow text-center bg-teal-50 hover:bg-teal-100 h-32 flex flex-col justify-center">
            <h3 className="raleway-title-h3 mb-2 text-teal-700">Customer Retention</h3>
            <p className="body-text text-gray-700">Keep your existing customers engaged and loyal to your brand.</p>
          </div>
        </Link>

        <Link to="/tools" className="no-underline">
          <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow text-center bg-blue-50 hover:bg-blue-100 h-32 flex flex-col justify-center">
            <h3 className="raleway-title-h3 mb-2 text-blue-700">Research Tools</h3>
            <p className="body-text text-gray-700">Create and analyze questionnaires for market research.</p>
          </div>
        </Link>
      </div>
    </div>
  );
}

export default Home;
